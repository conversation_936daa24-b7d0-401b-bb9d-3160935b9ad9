/**
 * تكوين إدارة الصفحات الفارغة
 * يساعد في منع أرشفة الصفحات التي لا تحتوي على محتوى
 */

export interface EmptyContentConfig {
  // منع الأرشفة للصفحات الفارغة
  preventIndexing: boolean
  // إظهار رسالة "قيد التطوير"
  showDevelopmentMessage: boolean
  // إعادة توجيه للصفحة الرئيسية للمادة
  redirectToSubject: boolean
  // الحد الأدنى لعدد التمارين لاعتبار الصفحة غير فارغة
  minExercisesCount: number
}

export const defaultEmptyContentConfig: EmptyContentConfig = {
  preventIndexing: true,
  showDevelopmentMessage: true,
  redirectToSubject: false,
  minExercisesCount: 1
}

/**
 * فحص إذا كانت الصفحة فارغة
 */
export function isContentEmpty(
  exercises: any[] | undefined | null,
  config: EmptyContentConfig = defaultEmptyContentConfig
): boolean {
  if (!exercises) return true
  return exercises.length < config.minExercisesCount
}

/**
 * إنشاء metadata للصفحات الفارغة
 */
export function createEmptyContentMetadata(
  title: string,
  type: 'lesson' | 'homework' | 'summary' | 'exam',
  config: EmptyContentConfig = defaultEmptyContentConfig
) {
  const typeText = {
    lesson: 'الدرس',
    homework: 'الفرض المنزلي', 
    summary: 'الملخص',
    exam: 'الامتحان'
  }[type]

  return {
    title: `${typeText} ${title} - قيد التطوير`,
    description: `${typeText} قيد التطوير وسيتم إضافة المحتوى قريباً`,
    robots: config.preventIndexing ? { index: false, follow: false } : undefined
  }
}

/**
 * قائمة بالصفحات التي يجب فحصها
 */
export const CONTENT_PAGES_TO_CHECK = [
  '/lesson/',
  '/homework/', 
  '/summary/',
  '/exam/'
] as const

/**
 * فحص إذا كان المسار يحتاج لفحص المحتوى
 */
export function shouldCheckContent(pathname: string): boolean {
  return CONTENT_PAGES_TO_CHECK.some(page => pathname.startsWith(page))
}
